[{"/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/main.js": "1", "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/App.vue": "2", "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/router/index.js": "3", "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/views/DrmMonitor.vue": "4"}, {"size": 440, "mtime": 1754128910000, "results": "5", "hashOfConfig": "6"}, {"size": 400, "mtime": 1754128924000, "results": "7", "hashOfConfig": "6"}, {"size": 319, "mtime": 1754128916000, "results": "8", "hashOfConfig": "6"}, {"size": 20434, "mtime": 1754359113000, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1q1v3c1", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/main.js", [], [], "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/App.vue", [], [], "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/router/index.js", [], [], "/Users/<USER>/work/workspace-xhs/20250805-html-edit/html-edit/src/views/DrmMonitor.vue", [], []]