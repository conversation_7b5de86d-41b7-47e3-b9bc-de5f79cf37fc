{"version": 3, "file": "button-custom.mjs", "sources": ["../../../../../../packages/components/button/src/button-custom.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { TinyColor } from '@ctrl/tinycolor'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\n\nimport type { ButtonProps } from './button'\n\nexport function darken(color: TinyColor, amount = 20) {\n  return color.mix('#141414', amount).toString()\n}\n\nexport function useButtonCustomStyle(props: ButtonProps) {\n  const _disabled = useFormDisabled()\n  const ns = useNamespace('button')\n\n  // calculate hover & active color by custom color\n  // only work when custom color\n  return computed(() => {\n    let styles: Record<string, string> = {}\n\n    let buttonColor = props.color\n\n    if (buttonColor) {\n      const match = (buttonColor as string).match(/var\\((.*?)\\)/)\n      if (match) {\n        buttonColor = window\n          .getComputedStyle(window.document.documentElement)\n          .getPropertyValue(match[1])\n      }\n      const color = new TinyColor(buttonColor)\n      const activeBgColor = props.dark\n        ? color.tint(20).toString()\n        : darken(color, 20)\n\n      if (props.plain) {\n        styles = ns.cssVarBlock({\n          'bg-color': props.dark\n            ? darken(color, 90)\n            : color.tint(90).toString(),\n          'text-color': buttonColor,\n          'border-color': props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString(),\n          'hover-text-color': `var(${ns.cssVarName('color-white')})`,\n          'hover-bg-color': buttonColor,\n          'hover-border-color': buttonColor,\n          'active-bg-color': activeBgColor,\n          'active-text-color': `var(${ns.cssVarName('color-white')})`,\n          'active-border-color': activeBgColor,\n        })\n\n        if (_disabled.value) {\n          styles[ns.cssVarBlockName('disabled-bg-color')] = props.dark\n            ? darken(color, 90)\n            : color.tint(90).toString()\n          styles[ns.cssVarBlockName('disabled-text-color')] = props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString()\n          styles[ns.cssVarBlockName('disabled-border-color')] = props.dark\n            ? darken(color, 80)\n            : color.tint(80).toString()\n        }\n      } else {\n        const hoverBgColor = props.dark\n          ? darken(color, 30)\n          : color.tint(30).toString()\n        const textColor = color.isDark()\n          ? `var(${ns.cssVarName('color-white')})`\n          : `var(${ns.cssVarName('color-black')})`\n        styles = ns.cssVarBlock({\n          'bg-color': buttonColor,\n          'text-color': textColor,\n          'border-color': buttonColor,\n          'hover-bg-color': hoverBgColor,\n          'hover-text-color': textColor,\n          'hover-border-color': hoverBgColor,\n          'active-bg-color': activeBgColor,\n          'active-border-color': activeBgColor,\n        })\n\n        if (_disabled.value) {\n          const disabledButtonColor = props.dark\n            ? darken(color, 50)\n            : color.tint(50).toString()\n          styles[ns.cssVarBlockName('disabled-bg-color')] = disabledButtonColor\n          styles[ns.cssVarBlockName('disabled-text-color')] = props.dark\n            ? 'rgba(255, 255, 255, 0.5)'\n            : `var(${ns.cssVarName('color-white')})`\n          styles[ns.cssVarBlockName('disabled-border-color')] =\n            disabledButtonColor\n        }\n      }\n    }\n\n    return styles\n  })\n}\n"], "names": [], "mappings": ";;;;;AAIO,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,EAAE;AAC3C,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjD,CAAC;AACM,SAAS,oBAAoB,CAAC,KAAK,EAAE;AAC5C,EAAE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;AACtC,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACpC,EAAE,OAAO,QAAQ,CAAC,MAAM;AACxB,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;AAClC,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACtD,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1G,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC;AAC/C,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACvF,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE;AACvB,QAAQ,MAAM,GAAG,EAAE,CAAC,WAAW,CAAC;AAChC,UAAU,UAAU,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;AAChF,UAAU,YAAY,EAAE,WAAW;AACnC,UAAU,cAAc,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;AACpF,UAAU,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AACpE,UAAU,gBAAgB,EAAE,WAAW;AACvC,UAAU,oBAAoB,EAAE,WAAW;AAC3C,UAAU,iBAAiB,EAAE,aAAa;AAC1C,UAAU,mBAAmB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AACrE,UAAU,qBAAqB,EAAE,aAAa;AAC9C,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;AAC7B,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACvH,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACzH,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC3H,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACxF,QAAQ,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3H,QAAQ,MAAM,GAAG,EAAE,CAAC,WAAW,CAAC;AAChC,UAAU,UAAU,EAAE,WAAW;AACjC,UAAU,YAAY,EAAE,SAAS;AACjC,UAAU,cAAc,EAAE,WAAW;AACrC,UAAU,gBAAgB,EAAE,YAAY;AACxC,UAAU,kBAAkB,EAAE,SAAS;AACvC,UAAU,oBAAoB,EAAE,YAAY;AAC5C,UAAU,iBAAiB,EAAE,aAAa;AAC1C,UAAU,qBAAqB,EAAE,aAAa;AAC9C,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;AAC7B,UAAU,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjG,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAChF,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,0BAA0B,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/I,UAAU,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,GAAG,mBAAmB,CAAC;AACpF,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL;;;;"}