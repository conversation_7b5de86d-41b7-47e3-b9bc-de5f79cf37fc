# Installation
> `npm install --save @types/web-bluetooth`

# Summary
This package contains type definitions for Web Bluetooth (https://webbluetoothcg.github.io/web-bluetooth/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/web-bluetooth.

### Additional Details
 * Last updated: Sat, 15 Oct 2022 06:33:03 GMT
 * Dependencies: none
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/urish), [<PERSON>](https://github.com/xlozinguez), [<PERSON>](https://github.com/thegecko), and [<PERSON>](https://github.com/DaBs).
