{"name": "@vue/cli-plugin-router", "version": "5.0.8", "description": "router plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-router"}, "keywords": ["vue", "cli", "router"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-router#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.8"}, "devDependencies": {"@vue/cli-test-utils": "^5.0.8"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0"}, "gitHead": "b154dbd7aca4b4538e6c483b1d4b817499d7b8eb"}