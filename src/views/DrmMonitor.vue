<template>
  <div class="drm-monitor">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-item">
        <i class="fas fa-satellite"></i>
        <span>DRM数字广播适配器</span>
      </div>
      <div class="status-item">
        <i class="fas fa-network-wired"></i>
        <span>当前IP: <span class="status-value">{{ systemInfo.currentIP }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-memory"></i>
        <span>内存占用: <span class="status-value">{{ systemInfo.memoryUsage }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-microchip"></i>
        <span>CPU: <span class="status-value">{{ systemInfo.cpuUsage }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-clock"></i>
        <span>时间: <span class="status-value">{{ currentTime }}</span></span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧服务总览区域 -->
      <div class="spectrum-section">
        <h2 class="section-title">
          <i class="fas fa-chart-line"></i>
          服务总览
        </h2>

        <!-- 服务卡片区域 -->
        <div class="service-overview">
          <div class="service-row">
            <el-card
              v-for="service in services"
              :key="service.id"
              class="service-card"
              shadow="hover"
            >
              <div class="service-content">
                <div class="service-title">{{ service.name }}</div>
                <div class="service-info">
                  <div class="service-label">CNR{{ service.id }}: {{ service.address }}</div>
                  <div class="service-label">服务: {{ service.serviceType }}</div>
                  <div class="service-label">类型: {{ service.type }}</div>
                </div>
                <div class="service-status" :class="service.status">
                  <i :class="service.status === 'online' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
                  {{ service.status === 'online' ? '正常' : '异常' }}
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- MDI流信息区域 -->
        <div class="signal-quality">
          <h2 class="section-title">
            <i class="fas fa-signal"></i>
            MDI流信息
          </h2>
          <div class="mdi-progress">
            <div class="progress-label">文件传输进度</div>
            <el-progress
              :percentage="mdiProgress"
              :color="progressColor"
              :stroke-width="8"
              :show-text="true"
            />
            <div class="progress-info">
              <span>传输速度: {{ transferSpeed }}</span>
              <span>剩余时间: {{ remainingTime }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 信道状态 -->
        <div class="channels-section">
          <h2 class="section-title">
            <i class="fas fa-list"></i>
            信道状态
          </h2>
          <div class="channels-list">
            <div
              v-for="channel in channels"
              :key="channel.id"
              class="channel-item"
              :class="{ active: channel.active }"
              @click="selectChannel(channel)"
            >
              <div class="channel-info">
                <div class="channel-name">{{ channel.name }}</div>
                <div class="channel-freq">{{ channel.value }}</div>
              </div>
              <div class="channel-status">
                <div class="status-dot" :class="{ active: channel.active }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据流监控 -->
        <div class="data-section">
          <h2 class="section-title">
            <i class="fas fa-stream"></i>
            数据流监控
          </h2>
          <div class="data-stream" ref="dataStream">
            <div
              v-for="(log, index) in dataLogs"
              :key="index"
              class="data-line"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部控制区 -->
    <div class="controls">
      <div class="control-group">
        <button class="control-btn" @click="handleSearch">
          <i class="fas fa-search"></i>
          <span></span>
        </button>
        <button class="control-btn" @click="handleSettings">
          <i class="fas fa-cog"></i>
          <span></span>
        </button>
        <button class="control-btn" @click="handleDatabase">
          <i class="fas fa-database"></i>
          <span></span>
        </button>
      </div>
      <div class="control-group">
        <button class="control-btn" @click="toggleRemoteLocal">
          <i class="fas fa-exchange-alt"></i>
          <span>{{ isRemote ? '远程' : '本地' }}</span>
        </button>
        <button class="control-btn primary" @click="togglePower">
          <i class="fas fa-power-off"></i>
          <span>{{ isPowered ? '停止' : '启动' }}</span>
        </button>
        <button class="control-btn" @click="showInfo">
          <i class="fas fa-info-circle"></i>
          <span>信息</span>
        </button>
      </div>
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="{ connected: isConnected }">
      <i :class="isConnected ? 'fas fa-wifi' : 'fas fa-wifi-slash'"></i>
      {{ isConnected ? '已连接' : '未连接' }}
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'DrmMonitor',
  setup() {
    // 响应式数据
    const currentTime = ref('')
    const isConnected = ref(false)
    const isPowered = ref(false)
    const isRemote = ref(true)
    const mdiProgress = ref(0)
    const transferSpeed = ref('0 KB/s')
    const remainingTime = ref('--:--')

    // 系统信息
    const systemInfo = reactive({
      currentIP: '**************',
      memoryUsage: '42%',
      cpuUsage: '42%'
    })

    // 服务数据
    const services = reactive([
      {
        id: 1,
        name: '服务1',
        address: '*************:8080',
        serviceType: '数据采集',
        type: '应急/普通',
        status: 'online'
      },
      {
        id: 2,
        name: '服务2',
        address: '*************:8081',
        serviceType: '信号处理',
        type: '应急/普通',
        status: 'online'
      },
      {
        id: 3,
        name: '服务3',
        address: '*************:8082',
        serviceType: '数据转发',
        type: '应急/普通',
        status: 'offline'
      },
      {
        id: 4,
        name: '服务4',
        address: '*************:8083',
        serviceType: '监控管理',
        type: '应急/普通',
        status: 'online'
      }
    ])

    // 信道数据
    const channels = reactive([
      {
        id: 1,
        name: '数据的属性',
        value: 'xxx JL',
        active: true
      },
      {
        id: 2,
        name: '广播类型',
        value: '应急/普通',
        active: false
      },
      {
        id: 3,
        name: '传输进度条',
        value: 'xx%',
        active: false
      },
      {
        id: 4,
        name: '当前码率',
        value: '64 kbps',
        active: false
      }
    ])

    // 数据日志
    const dataLogs = ref([])

    // WebSocket连接
    let websocket = null
    let reconnectTimer = null
    let dataRequestTimer = null

    // 计算属性
    const progressColor = ref('#67c23a')

    // 方法
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
    }

    const connectWebSocket = () => {
      try {
        // 这里应该连接到您的WebSocket服务器
        // WebSocket服务器需要代理TCP连接到**************:9080
        websocket = new WebSocket('ws://localhost:8080/ws')

        websocket.onopen = () => {
          console.log('WebSocket连接已建立')
          isConnected.value = true
          ElMessage.success('连接成功')
          startDataRequest()
        }

        websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            handleReceivedData(data)
          } catch (error) {
            console.error('解析数据失败:', error)
          }
        }

        websocket.onclose = () => {
          console.log('WebSocket连接已关闭')
          isConnected.value = false
          stopDataRequest()
          // 自动重连
          reconnectTimer = setTimeout(connectWebSocket, 5000)
        }

        websocket.onerror = (error) => {
          console.error('WebSocket错误:', error)
          ElMessage.error('连接错误')
        }
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        ElMessage.error('连接失败')
      }
    }

    const startDataRequest = () => {
      // 每秒请求一次数据
      dataRequestTimer = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
          websocket.send(JSON.stringify({
            type: 'request_data',
            timestamp: Date.now()
          }))
        }
      }, 1000)
    }

    const stopDataRequest = () => {
      if (dataRequestTimer) {
        clearInterval(dataRequestTimer)
        dataRequestTimer = null
      }
    }

    const handleReceivedData = (data) => {
      // 处理接收到的数据
      if (data.type === 'system_info') {
        Object.assign(systemInfo, data.data)
      } else if (data.type === 'services') {
        Object.assign(services, data.data)
      } else if (data.type === 'channels') {
        Object.assign(channels, data.data)
      } else if (data.type === 'mdi_progress') {
        mdiProgress.value = data.progress
        transferSpeed.value = data.speed
        remainingTime.value = data.remaining
      } else if (data.type === 'log') {
        addDataLog(data.message)
      }
    }

    const addDataLog = (message) => {
      const now = new Date()
      const timeStr = `[${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}]`
      dataLogs.value.push(`${timeStr} ${message}`)

      // 保持最多15条日志
      if (dataLogs.value.length > 15) {
        dataLogs.value.shift()
      }

      // 滚动到底部
      setTimeout(() => {
        const dataStream = document.querySelector('.data-stream')
        if (dataStream) {
          dataStream.scrollTop = dataStream.scrollHeight
        }
      }, 50)
    }

    // 模拟数据更新（当没有真实连接时）
    const simulateData = () => {
      // 模拟MDI进度
      mdiProgress.value = Math.min(100, mdiProgress.value + Math.random() * 2)
      transferSpeed.value = `${(Math.random() * 100 + 50).toFixed(1)} KB/s`

      // 模拟日志
      const messages = [
        '接收数据包: 1024 bytes | CRC: OK',
        '音频流解码: AAC+ 24kbps',
        '文本数据: 天气预报: 北京 晴 25°C',
        '信号质量更新: SNR 24.8dB',
        '数据缓存: 85% | 释放缓冲区',
        '节目信息: 中国之声 - 新闻直播间',
        '实时交通: 东三环畅通'
      ]

      if (Math.random() > 0.7) {
        const randomMessage = messages[Math.floor(Math.random() * messages.length)]
        addDataLog(randomMessage)
      }
    }

    // 事件处理方法
    const selectChannel = (channel) => {
      channels.forEach(ch => {
        ch.active = false
      })
      channel.active = true
    }

    const handleSearch = () => {
      ElMessage.info('搜索功能')
    }

    const handleSettings = () => {
      ElMessage.info('设置功能')
    }

    const handleDatabase = () => {
      ElMessage.info('数据库功能')
    }

    const toggleRemoteLocal = () => {
      isRemote.value = !isRemote.value
      ElMessage.success(`切换到${isRemote.value ? '远程' : '本地'}模式`)
    }

    const togglePower = () => {
      isPowered.value = !isPowered.value
      if (isPowered.value) {
        connectWebSocket()
        ElMessage.success('系统启动')
      } else {
        if (websocket) {
          websocket.close()
        }
        ElMessage.warning('系统停止')
      }
    }

    const showInfo = () => {
      ElMessage.info('系统信息')
    }

    // 生命周期
    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)

      // 启动模拟数据（用于测试）
      setInterval(simulateData, 2000)

      // 初始化一些日志
      addDataLog('系统初始化完成')
      addDataLog('等待连接到服务器...')
    })

    onUnmounted(() => {
      if (websocket) {
        websocket.close()
      }
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
      }
      stopDataRequest()
    })

    return {
      currentTime,
      isConnected,
      isPowered,
      isRemote,
      mdiProgress,
      transferSpeed,
      remainingTime,
      progressColor,
      systemInfo,
      services,
      channels,
      dataLogs,
      selectChannel,
      handleSearch,
      handleSettings,
      handleDatabase,
      toggleRemoteLocal,
      togglePower,
      showInfo
    }
  }
}
</script>

<style scoped>
:root {
  --primary: #1a73e8;
  --secondary: #0d47a1;
  --accent: #00c853;
  --dark: #121212;
  --darker: #0a0a0a;
  --light: #f5f5f5;
  --gray: #2d2d2d;
  --warning: #ff9800;
  --danger: #f44336;
}

.drm-monitor {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--darker), var(--dark));
  color: var(--light);
  padding: 20px;
}

/* 顶部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(20, 20, 30, 0.9);
  border-radius: 15px 15px 0 0;
  border-bottom: 1px solid rgba(80, 80, 100, 0.3);
  margin-bottom: 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-value {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--accent);
}

/* 主内容区 */
.main-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
  background: rgba(15, 15, 15, 0.85);
  padding: 25px;
  border-radius: 0;
}

/* 左侧服务总览区域 */
.spectrum-section {
  flex: 3;
  background: rgba(25, 25, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 1.3rem;
  margin-bottom: 0px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #bbbbff;
}

.section-title i {
  color: var(--accent);
}

/* 服务卡片区域 */
.service-overview {
  flex: 1;
  margin-bottom: 25px;
}

.service-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-card {
  background: rgba(30, 30, 40, 0.8) !important;
  border: 1px solid rgba(80, 80, 120, 0.3) !important;
  border-radius: 10px !important;
}

.service-card :deep(.el-card__body) {
  padding: 15px;
}

.service-content {
  color: var(--light);
}

.service-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #bbbbff;
}

.service-info {
  margin-bottom: 10px;
}

.service-label {
  font-size: 0.9rem;
  color: #aaa;
  margin-bottom: 3px;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  font-weight: 500;
}

.service-status.online {
  color: var(--accent);
}

.service-status.offline {
  color: var(--danger);
}

/* MDI流信息区域 */
.signal-quality {
  background: rgba(20, 20, 30, 0.6);
  border-radius: 8px;
  padding: 20px;
}

.mdi-progress {
  margin-top: 15px;
}

.progress-label {
  margin-bottom: 10px;
  font-size: 1rem;
  color: #bbbbff;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #aaa;
}

/* 右侧面板 */
.right-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.channels-section, .data-section {
  background: rgba(25, 25, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
}

.channels-list {
  margin-top: 15px;
  max-height: 333px;
  overflow-y: auto;
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: rgba(40, 40, 50, 0.5);
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.channel-item:hover {
  background: rgba(50, 50, 70, 0.6);
}

.channel-item.active {
  border-color: var(--accent);
  background: rgba(26, 115, 232, 0.15);
}

.channel-info {
  display: flex;
  flex-direction: column;
}

.channel-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.channel-freq {
  font-size: 0.85rem;
  color: #aaa;
}

.channel-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--danger);
}

.status-dot.active {
  background: var(--accent);
}

/* 数据流监控 */
.data-stream {
  background: rgba(10, 10, 15, 0.8);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  min-height: 20px;
  max-height: 80px;
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  line-height: 1.6;
  overflow-y: auto;
  border: 1px solid rgba(80, 80, 120, 0.3);
}

.data-line {
  color: #aaffaa;
  margin-bottom: 2px;
}

.data-line:nth-child(2n) {
  color: #bbbbff;
}

/* 底部控制区 */
.controls {
  display: flex;
  padding: 20px 30px;
  background: rgba(20, 20, 30, 0.9);
  border-radius: 0 0 15px 15px;
  border-top: 1px solid rgba(80, 80, 100, 0.3);
  gap: 20px;
  margin-top: 0;
}

.control-group {
  flex: 1;
  display: flex;
  gap: 20px;
}

.control-btn {
  flex: 1;
  background: rgba(40, 40, 60, 0.8);
  border: none;
  border-radius: 8px;
  color: var(--light);
  padding: 15px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
  min-height: 80px;
}

.control-btn:hover {
  background: rgba(50, 50, 80, 0.9);
  transform: translateY(-2px);
}

.control-btn i {
  font-size: 1.8rem;
  color: var(--accent);
}

.control-btn.primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.control-btn.primary i {
  color: white;
}

/* 连接状态指示器 */
.connection-status {
  position: fixed;
  top: 66px;
  right: 94px;
  background: rgba(244, 67, 54, 0.9);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  transition: all 0.3s;
}

.connection-status.connected {
  background: rgba(0, 200, 83, 0.9);
}

.connection-status i {
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 1100px) {
  .main-content {
    flex-direction: column;
  }

  .service-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .drm-monitor {
    padding: 10px;
  }

  .status-bar {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .service-row {
    grid-template-columns: 1fr;
  }

  .controls {
    flex-direction: column;
    gap: 15px;
  }

  .control-group {
    flex-direction: column;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-progress-bar__outer) {
  background-color: rgba(40, 40, 50, 0.8) !important;
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease !important;
}

:deep(.el-card) {
  background-color: transparent !important;
  border: none !important;
}

/* 滚动条样式 */
.data-stream::-webkit-scrollbar,
.channels-list::-webkit-scrollbar {
  width: 6px;
}

.data-stream::-webkit-scrollbar-track,
.channels-list::-webkit-scrollbar-track {
  background: rgba(40, 40, 50, 0.5);
  border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb,
.channels-list::-webkit-scrollbar-thumb {
  background: rgba(100, 100, 120, 0.6);
  border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb:hover,
.channels-list::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 140, 0.8);
}
</style>
