<template>
  <div class="drm-monitor">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-item">
        <i class="fas fa-satellite"></i>
        <span>DRM数字广播适配器</span>
      </div>
      <div class="status-item">
        <i class="fas fa-network-wired"></i>
        <span>当前IP: <span class="status-value">{{ systemInfo.currentIP }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-memory"></i>
        <span>内存占用: <span class="status-value">{{ systemInfo.memoryUsage }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-microchip"></i>
        <span>CPU: <span class="status-value">{{ systemInfo.cpuUsage }}</span></span>
      </div>
      <div class="status-item">
        <i class="fas fa-clock"></i>
        <span>时间: <span class="status-value">{{ currentTime }}</span></span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧服务总览区域 -->
      <div class="spectrum-section">
        <h2 class="section-title">
          <i class="fas fa-chart-line"></i>
          服务总览
        </h2>

        <!-- 服务卡片区域 -->
        <div class="service-overview">
          <div class="service-row">
            <el-card
              v-for="service in services"
              :key="service.id"
              class="service-card"
              shadow="hover"
            >
              <div class="service-content">
                <div class="service-title">{{ service.serviceLabel }}</div>
                <div class="service-info">
                  <div class="service-label">服务: {{ service.serviceLabel }}</div>
                  <div class="service-label">类型: {{ service.serviceType }}</div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- MDI流信息区域 -->
        <div class="signal-quality">
          <h2 class="section-title">
            <i class="fas fa-signal"></i>
            MDI流信息
          </h2>
          <div class="mdi-progress">
            <div class="progress-label">当前文件: {{ currentFileName }}</div>
            <div class="progress-label">传输进度</div>
            <el-progress
              :percentage="mdiProgress"
              :color="progressColor"
              :stroke-width="8"
              :show-text="true"
            />
            <div class="progress-info">
              <span>传输速度: {{ transferSpeed }}</span>
              <span>剩余时间: {{ remainingTime }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 信道状态 -->
        <div class="channels-section">
          <h2 class="section-title">
            <i class="fas fa-list"></i>
            信道状态
          </h2>
          <div class="channels-list">
            <div
              v-for="channel in channels"
              :key="channel.id"
              class="channel-item"
              :class="{ active: channel.active }"
              @click="selectChannel(channel)"
            >
              <div class="channel-info">
                <div class="channel-name">{{ channel.name }}</div>
                <div class="channel-freq">{{ channel.value }}</div>
              </div>
              <div class="channel-status">
                <div class="status-dot" :class="{ active: channel.active }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据流监控 -->
        <div class="data-section">
          <h2 class="section-title">
            <i class="fas fa-stream"></i>
            数据流监控
          </h2>
          <div class="data-stream" ref="dataStream">
            <div
              v-for="(log, index) in dataLogs"
              :key="index"
              class="data-line"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部控制区 -->
    <div class="controls">
      <div class="control-group">
        <button class="control-btn" @click="handleRefresh">
          <i class="fas fa-sync-alt"></i>
          <span>刷新</span>
        </button>
        <button class="control-btn" @click="handleSearch">
          <i class="fas fa-search"></i>
          <span></span>
        </button>
        <button class="control-btn" @click="handleSettings">
          <i class="fas fa-cog"></i>
          <span></span>
        </button>
        <button class="control-btn" @click="handleDatabase">
          <i class="fas fa-database"></i>
          <span></span>
        </button>
      </div>
      <div class="control-group">
        <button class="control-btn" @click="toggleRemoteLocal">
          <i class="fas fa-exchange-alt"></i>
          <span>{{ isRemote ? '远程' : '本地' }}</span>
        </button>
        <button class="control-btn primary" :class="{ active: isConnected }" disabled>
          <i class="fas fa-power-off"></i>
          <span>{{ isConnected ? '运行中' : '已停止' }}</span>
        </button>
        <button class="control-btn" @click="showInfo">
          <i class="fas fa-info-circle"></i>
          <span>信息</span>
        </button>
      </div>
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="{ connected: isConnected }">
      <i :class="isConnected ? 'fas fa-wifi' : 'fas fa-wifi-slash'"></i>
      {{ isConnected ? '已连接' : '未连接' }}
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'DrmMonitor',
  setup() {
    // 响应式数据
    const currentTime = ref('')
    const isConnected = ref(false)
    const isPowered = ref(false)
    const isRemote = ref(true)
    const mdiProgress = ref(0)
    const transferSpeed = ref('0 KB/s')
    const remainingTime = ref('--:--')
    const currentFileName = ref('--')

    // 系统信息
    const systemInfo = reactive({
      currentIP: '*************',
      memoryUsage: '42%',
      cpuUsage: '42%'
    })

    // 服务数据 - 根据新的数据格式调整
    const services = reactive([
      {
        id: 1,
        serviceLabel: '服务1',
        serviceType: '数据采集'
      },
      {
        id: 2,
        serviceLabel: '服务2',
        serviceType: '信号处理'
      },
      {
        id: 3,
        serviceLabel: '服务3',
        serviceType: '数据转发'
      },
      {
        id: 4,
        serviceLabel: '服务4',
        serviceType: '监控管理'
      }
    ])

    // 信道数据 - 根据新的ChannelStatus格式调整
    const channels = reactive([
      {
        id: 1,
        name: '数据的属性',
        value: 'xxx JL',
        active: true
      },
      {
        id: 2,
        name: '广播类型',
        value: '应急/普通',
        active: false
      },
      {
        id: 3,
        name: '当前码率',
        value: '64 kbps',
        active: false
      }
    ])

    // 数据日志
    const dataLogs = ref([])

    // WebSocket连接
    let websocket = null
    let reconnectTimer = null
    let dataRequestTimer = null
    let reconnectAttempts = 0
    const maxReconnectDelay = 5000 // 最大重连延迟5秒
    let isManualClose = false // 标记是否为手动关闭

    // 计算属性
    const progressColor = ref('#67c23a')

    // 方法
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
    }

    const connectWebSocket = () => {
      // 如果是手动关闭，不进行重连
      if (isManualClose) {
        return
      }

      try {
        // 清理之前的连接
        if (websocket) {
          websocket.close()
          websocket = null
        }

        console.log(`尝试连接WebSocket... (第${reconnectAttempts + 1}次)`)

        // 连接到WebSocket代理服务器
        // WebSocket服务器代理TCP连接到**************:9080
        websocket = new WebSocket('ws://localhost:8083')

        websocket.onopen = () => {
          console.log('WebSocket连接已建立')
          isConnected.value = true
          reconnectAttempts = 0 // 重置重连次数

          // 只在第一次连接成功时显示消息
          if (reconnectAttempts === 0) {
            ElMessage.success('连接成功')
          }

          startDataRequest()
        }

        websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            handleReceivedData(data)
          } catch (error) {
            console.error('解析数据失败:', error)
          }
        }

        websocket.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason)
          isConnected.value = false
          stopDataRequest()

          // 如果不是手动关闭，立即尝试重连
          if (!isManualClose) {
            scheduleReconnect()
          }
        }

        websocket.onerror = (error) => {
          console.error('WebSocket错误:', error)
          isConnected.value = false

          // 发生错误时也尝试重连
          if (!isManualClose) {
            scheduleReconnect()
          }
        }
      } catch (error) {
        console.error('WebSocket连接失败:', error)

        // 连接失败时也尝试重连
        if (!isManualClose) {
          scheduleReconnect()
        }
      }
    }

    // 安排重连
    const scheduleReconnect = () => {
      if (isManualClose) {
        return
      }

      // 清理之前的重连定时器
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
      }

      // 计算重连延迟（指数退避，但有最大限制）
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), maxReconnectDelay)
      reconnectAttempts++

      console.log(`将在${delay}ms后尝试重连...`)

      reconnectTimer = setTimeout(() => {
        connectWebSocket()
      }, delay)
    }

    const startDataRequest = () => {
      // 每秒请求一次数据
      dataRequestTimer = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
          websocket.send(JSON.stringify({
            type: 'request_data',
            timestamp: Date.now()
          }))
        }
      }, 1000)
    }

    const stopDataRequest = () => {
      if (dataRequestTimer) {
        clearInterval(dataRequestTimer)
        dataRequestTimer = null
      }
    }

    const handleReceivedData = (data) => {
      // 处理接收到的数据 - 根据新的JSON格式
      console.log('收到数据:', data)

      // 处理信道状态
      if (data.ChannelStatus) {
        const channelStatus = data.ChannelStatus

        // 更新信道数据
        if (channelStatus.DataAttribute) {
          const dataAttrChannel = channels.find(ch => ch.name === '数据的属性')
          if (dataAttrChannel) {
            dataAttrChannel.value = channelStatus.DataAttribute
          }
        }

        if (channelStatus.BroadcastType) {
          const broadcastChannel = channels.find(ch => ch.name === '广播类型')
          if (broadcastChannel) {
            broadcastChannel.value = channelStatus.BroadcastType
          }
        }

        if (channelStatus.CurrentBitrate) {
          const bitrateChannel = channels.find(ch => ch.name === '当前码率')
          if (bitrateChannel) {
            bitrateChannel.value = channelStatus.CurrentBitrate
          }
        }
      }

      // 处理当前文件名
      if (data.CurrentFileName) {
        currentFileName.value = data.CurrentFileName
      }

      // 处理传输进度
      if (data.TransmissionProgress) {
        // 假设传输进度是百分比格式，如 "75%"
        const progressValue = parseInt(data.TransmissionProgress.replace('%', ''))
        if (!isNaN(progressValue)) {
          mdiProgress.value = progressValue
        }
      }

      // 处理服务总览
      if (data.ServiceOverview && Array.isArray(data.ServiceOverview)) {
        // 清空现有服务数据
        services.splice(0, services.length)

        // 添加新的服务数据
        data.ServiceOverview.forEach((service, index) => {
          const serviceData = {
            id: index + 1,
            serviceLabel: service.ServiceLabel1 || service.ServiceLabel2 || service.ServiceLabel3 || service.ServiceLabel4 || `服务${index + 1}`,
            serviceType: service.ServiceType || '未知类型'
          }
          services.push(serviceData)
        })
      }

      // 兼容旧格式的数据处理
      if (data.type === 'data_update') {
        if (data.logs && Array.isArray(data.logs)) {
          data.logs.forEach(log => addDataLog(log))
        } else if (data.logs) {
          addDataLog(data.logs)
        }
      } else if (data.type === 'connection_status') {
        addDataLog(data.message || '连接状态更新')
      }
    }

    const addDataLog = (message) => {
      const now = new Date()
      const timeStr = `[${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}]`
      dataLogs.value.push(`${timeStr} ${message}`)

      // 保持最多15条日志
      if (dataLogs.value.length > 15) {
        dataLogs.value.shift()
      }

      // 滚动到底部
      setTimeout(() => {
        const dataStream = document.querySelector('.data-stream')
        if (dataStream) {
          dataStream.scrollTop = dataStream.scrollHeight
        }
      }, 50)
    }

    // 模拟数据更新（当没有真实连接时）
    const simulateData = () => {
      // 模拟MDI进度
      mdiProgress.value = Math.min(100, mdiProgress.value + Math.random() * 2)
      transferSpeed.value = `${(Math.random() * 100 + 50).toFixed(1)} KB/s`

      // 模拟日志
      const messages = [
        '接收数据包: 1024 bytes | CRC: OK',
        '音频流解码: AAC+ 24kbps',
        '文本数据: 天气预报: 北京 晴 25°C',
        '信号质量更新: SNR 24.8dB',
        '数据缓存: 85% | 释放缓冲区',
        '节目信息: 中国之声 - 新闻直播间',
        '实时交通: 东三环畅通'
      ]

      if (Math.random() > 0.7) {
        const randomMessage = messages[Math.floor(Math.random() * messages.length)]
        addDataLog(randomMessage)
      }
    }

    // 事件处理方法
    const selectChannel = (channel) => {
      channels.forEach(ch => {
        ch.active = false
      })
      channel.active = true
    }

    const handleRefresh = () => {
      // 刷新数据而不断开WebSocket连接
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        // 立即请求新数据
        websocket.send(JSON.stringify({
          type: 'request_data',
          timestamp: Date.now()
        }))
        ElMessage.success('数据已刷新')
      } else {
        ElMessage.warning('连接未建立，无法刷新数据')
      }
    }

    const handleSearch = () => {
      ElMessage.info('搜索功能')
    }

    const handleSettings = () => {
      ElMessage.info('设置功能')
    }

    const handleDatabase = () => {
      ElMessage.info('数据库功能')
    }

    const toggleRemoteLocal = () => {
      isRemote.value = !isRemote.value
      ElMessage.success(`切换到${isRemote.value ? '远程' : '本地'}模式`)
    }

    const togglePower = () => {
      // 移除用户控制权限，按钮仅作为状态显示
      ElMessage.info('系统状态由程序自动管理')
    }

    const showInfo = () => {
      ElMessage.info('系统信息')
    }

    // 断开连接的方法（仅在组件卸载时使用）
    const disconnectWebSocket = () => {
      isManualClose = true

      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }

      if (websocket) {
        websocket.close()
        websocket = null
      }

      stopDataRequest()
      isConnected.value = false
    }

    // 页面可见性变化处理
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时不断开连接，保持连接状态
        console.log('页面隐藏，保持WebSocket连接')
      } else {
        // 页面显示时确保连接正常
        console.log('页面显示，检查WebSocket连接状态')
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
          isManualClose = false
          connectWebSocket()
        }
      }
    }

    // 生命周期
    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)

      // 启动模拟数据（用于测试）
      setInterval(simulateData, 2000)

      // 初始化一些日志
      addDataLog('系统初始化完成')
      addDataLog('正在连接到服务器...')

      // 页面加载时自动连接WebSocket
      isManualClose = false
      connectWebSocket()

      // 监听页面可见性变化
      document.addEventListener('visibilitychange', handleVisibilityChange)

      // 监听页面刷新前事件，确保不断开连接
      window.addEventListener('beforeunload', (event) => {
        // 页面刷新时不设置手动关闭标志，让连接保持
        console.log('页面即将刷新，保持WebSocket连接状态')
      })
    })

    onUnmounted(() => {
      // 组件卸载时才真正断开连接
      disconnectWebSocket()

      // 移除事件监听器
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    })

    return {
      currentTime,
      isConnected,
      isPowered,
      isRemote,
      mdiProgress,
      transferSpeed,
      remainingTime,
      currentFileName,
      progressColor,
      systemInfo,
      services,
      channels,
      dataLogs,
      selectChannel,
      handleRefresh,
      handleSearch,
      handleSettings,
      handleDatabase,
      toggleRemoteLocal,
      togglePower,
      showInfo
    }
  }
}
</script>

<style scoped>
:root {
  --primary: #1a73e8;
  --secondary: #0d47a1;
  --accent: #00c853;
  --dark: #121212;
  --darker: #0a0a0a;
  --light: #f5f5f5;
  --gray: #2d2d2d;
  --warning: #ff9800;
  --danger: #f44336;
}

.drm-monitor {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--darker), var(--dark));
  color: var(--light);
  padding: 20px;
}

/* 顶部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(20, 20, 30, 0.9);
  border-radius: 15px 15px 0 0;
  border-bottom: 1px solid rgba(80, 80, 100, 0.3);
  margin-bottom: 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-value {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--accent);
}

/* 主内容区 */
.main-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
  background: rgba(15, 15, 15, 0.85);
  padding: 25px;
  border-radius: 0;
}

/* 左侧服务总览区域 */
.spectrum-section {
  flex: 3;
  background: rgba(25, 25, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 1.3rem;
  margin-bottom: 0px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #bbbbff;
}

.section-title i {
  color: var(--accent);
}

/* 服务卡片区域 */
.service-overview {
  flex: 1;
  margin-bottom: 25px;
}

.service-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-card {
  background: rgba(30, 30, 40, 0.8) !important;
  border: 1px solid rgba(80, 80, 120, 0.3) !important;
  border-radius: 10px !important;
}

.service-card :deep(.el-card__body) {
  padding: 15px;
}

.service-content {
  color: var(--light);
}

.service-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #bbbbff;
}

.service-info {
  margin-bottom: 10px;
}

.service-label {
  font-size: 0.9rem;
  color: #aaa;
  margin-bottom: 3px;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  font-weight: 500;
}

.service-status.online {
  color: var(--accent);
}

.service-status.offline {
  color: var(--danger);
}

/* MDI流信息区域 */
.signal-quality {
  background: rgba(20, 20, 30, 0.6);
  border-radius: 8px;
  padding: 20px;
}

.mdi-progress {
  margin-top: 15px;
}

.progress-label {
  margin-bottom: 10px;
  font-size: 1rem;
  color: #bbbbff;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #aaa;
}

/* 右侧面板 */
.right-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.channels-section, .data-section {
  background: rgba(25, 25, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
}

.channels-list {
  margin-top: 15px;
  max-height: 333px;
  overflow-y: auto;
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: rgba(40, 40, 50, 0.5);
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.channel-item:hover {
  background: rgba(50, 50, 70, 0.6);
}

.channel-item.active {
  border-color: var(--accent);
  background: rgba(26, 115, 232, 0.15);
}

.channel-info {
  display: flex;
  flex-direction: column;
}

.channel-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.channel-freq {
  font-size: 0.85rem;
  color: #aaa;
}

.channel-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--danger);
}

.status-dot.active {
  background: var(--accent);
}

/* 数据流监控 */
.data-stream {
  background: rgba(10, 10, 15, 0.8);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  min-height: 20px;
  max-height: 80px;
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  line-height: 1.6;
  overflow-y: auto;
  border: 1px solid rgba(80, 80, 120, 0.3);
}

.data-line {
  color: #aaffaa;
  margin-bottom: 2px;
}

.data-line:nth-child(2n) {
  color: #bbbbff;
}

/* 底部控制区 */
.controls {
  display: flex;
  padding: 20px 30px;
  background: rgba(20, 20, 30, 0.9);
  border-radius: 0 0 15px 15px;
  border-top: 1px solid rgba(80, 80, 100, 0.3);
  gap: 20px;
  margin-top: 0;
}

.control-group {
  flex: 1;
  display: flex;
  gap: 20px;
}

.control-btn {
  flex: 1;
  background: rgba(40, 40, 60, 0.8);
  border: none;
  border-radius: 8px;
  color: var(--light);
  padding: 15px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
  min-height: 80px;
}

.control-btn:hover {
  background: rgba(50, 50, 80, 0.9);
  transform: translateY(-2px);
}

.control-btn i {
  font-size: 1.8rem;
  color: var(--accent);
}

.control-btn.primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.control-btn.primary i {
  color: white;
}

.control-btn.primary.active {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.control-btn:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.control-btn:disabled:hover {
  transform: none;
  background: rgba(40, 40, 60, 0.8);
}

.control-btn.primary:disabled:hover {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.control-btn.primary.active:disabled:hover {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

/* 连接状态指示器 */
.connection-status {
  position: fixed;
  top: 66px;
  right: 94px;
  background: rgba(244, 67, 54, 0.9);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  transition: all 0.3s;
}

.connection-status.connected {
  background: rgba(0, 200, 83, 0.9);
}

.connection-status i {
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 1100px) {
  .main-content {
    flex-direction: column;
  }

  .service-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .drm-monitor {
    padding: 10px;
  }

  .status-bar {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .service-row {
    grid-template-columns: 1fr;
  }

  .controls {
    flex-direction: column;
    gap: 15px;
  }

  .control-group {
    flex-direction: column;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-progress-bar__outer) {
  background-color: rgba(40, 40, 50, 0.8) !important;
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease !important;
}

:deep(.el-card) {
  background-color: transparent !important;
  border: none !important;
}

/* 滚动条样式 */
.data-stream::-webkit-scrollbar,
.channels-list::-webkit-scrollbar {
  width: 6px;
}

.data-stream::-webkit-scrollbar-track,
.channels-list::-webkit-scrollbar-track {
  background: rgba(40, 40, 50, 0.5);
  border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb,
.channels-list::-webkit-scrollbar-thumb {
  background: rgba(100, 100, 120, 0.6);
  border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb:hover,
.channels-list::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 140, 0.8);
}
</style>
