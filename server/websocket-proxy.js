const WebSocket = require('ws');
const net = require('net');

// WebSocket服务器配置
const WS_PORT = 8083;
const TCP_HOST = '*************';
const TCP_PORT = 9082;

// 创建WebSocket服务器
const wss = new WebSocket.Server({ port: WS_PORT });

console.log(`WebSocket代理服务器启动在端口 ${WS_PORT}`);
console.log(`将代理TCP连接到 ${TCP_HOST}:${TCP_PORT}`);

// 存储活跃的连接
const activeConnections = new Map();

wss.on('connection', (ws) => {
  console.log('新的WebSocket连接建立');
  
  // 为每个WebSocket连接创建唯一ID
  const connectionId = Date.now() + Math.random();
  
  // 创建TCP连接
  const tcpClient = new net.Socket();
  
  // 存储连接信息
  activeConnections.set(connectionId, {
    ws: ws,
    tcp: tcpClient,
    lastRequest: Date.now()
  });

  // 连接到TCP服务器
  tcpClient.connect(TCP_PORT, TCP_HOST, () => {
    console.log(`TCP连接已建立到 ${TCP_HOST}:${TCP_PORT}`);
    
    // 发送连接成功消息
    ws.send(JSON.stringify({
      type: 'connection_status',
      status: 'connected',
      message: 'TCP连接已建立'
    }));
  });

  // 处理TCP数据接收
  tcpClient.on('data', (data) => {
    try {
      // 假设TCP服务器返回JSON格式数据
      const jsonData = data.toString();
      
      // 发送数据到WebSocket客户端
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(jsonData);
      }
    } catch (error) {
      console.error('处理TCP数据时出错:', error);
    }
  });

  // 处理TCP连接错误
  tcpClient.on('error', (error) => {
    console.error('TCP连接错误:', error);
    
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'TCP连接错误: ' + error.message
      }));
    }
  });

  // 处理TCP连接关闭
  tcpClient.on('close', () => {
    console.log('TCP连接已关闭');
    
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'connection_status',
        status: 'disconnected',
        message: 'TCP连接已关闭'
      }));
    }
  });

  // 处理WebSocket消息
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      
      if (data.type === 'request_data') {
        // 向TCP服务器请求数据
        if (tcpClient.readyState === 'open') {
          // 发送数据请求到TCP服务器
          // const request = JSON.stringify({

        
            // command: 'get_data',
            // timestamp: data.timestamp
          // });

          const request = JSON.stringify({

           
            command: 'msc',
            
          });
          
          tcpClient.write(request);
        } else {
          // TCP连接未就绪，发送模拟数据
          sendMockData(ws);
        }
      }
    } catch (error) {
      console.error('处理WebSocket消息时出错:', error);
    }
  });

  // 处理WebSocket连接关闭
  ws.on('close', () => {
    console.log('WebSocket连接已关闭');
    
    // 关闭TCP连接
    if (tcpClient && !tcpClient.destroyed) {
      tcpClient.destroy();
    }
    
    // 清理连接记录
    activeConnections.delete(connectionId);
  });

  // 处理WebSocket错误
  ws.on('error', (error) => {
    console.error('WebSocket错误:', error);
  });
});

// 发送模拟数据（用于测试）
function sendMockData(ws) {
  if (ws.readyState !== WebSocket.OPEN) return;
  
  // const mockData = {
  //   type: 'data_update',
  //   timestamp: Date.now(),
  //   system_info: {
  //     currentIP: '**************',
  //     memoryUsage: Math.floor(Math.random() * 20 + 40) + '%',
  //     cpuUsage: Math.floor(Math.random() * 30 + 30) + '%'
  //   },
  //   services: [
  //     {
  //       id: 1,
  //       name: '服务1',
  //       address: '*************:8080',
  //       serviceType: '数据采集',
  //       type: '应急/普通',
  //       status: Math.random() > 0.1 ? 'online' : 'offline'
  //     },
  //     {
  //       id: 2,
  //       name: '服务2',
  //       address: '*************:8081',
  //       serviceType: '信号处理',
  //       type: '应急/普通',
  //       status: Math.random() > 0.1 ? 'online' : 'offline'
  //     },
  //     {
  //       id: 3,
  //       name: '服务3',
  //       address: '*************:8082',
  //       serviceType: '数据转发',
  //       type: '应急/普通',
  //       status: Math.random() > 0.2 ? 'online' : 'offline'
  //     },
  //     {
  //       id: 4,
  //       name: '服务4',
  //       address: '*************:8083',
  //       serviceType: '监控管理',
  //       type: '应急/普通',
  //       status: Math.random() > 0.1 ? 'online' : 'offline'
  //     }
  //   ],
  //   channels: [
  //     {
  //       id: 1,
  //       name: '数据的属性',
  //       value: 'xxx JL',
  //       active: true
  //     },
  //     {
  //       id: 2,
  //       name: '广播类型',
  //       value: '应急/普通',
  //       active: false
  //     },
  //     {
  //       id: 3,
  //       name: '传输进度条',
  //       value: Math.floor(Math.random() * 100) + '%',
  //       active: false
  //     },
  //     {
  //       id: 4,
  //       name: '当前码率',
  //       value: Math.floor(Math.random() * 64 + 32) + ' kbps',
  //       active: false
  //     }
  //   ],
  //   mdi_progress: {
  //     progress: Math.floor(Math.random() * 100),
  //     speed: (Math.random() * 100 + 50).toFixed(1) + ' KB/s',
  //     remaining: Math.floor(Math.random() * 300) + 's'
  //   },
  //   logs: [
  //     '接收数据包: ' + Math.floor(Math.random() * 1024 + 512) + ' bytes | CRC: OK',
  //     '音频流解码: AAC+ ' + Math.floor(Math.random() * 20 + 20) + 'kbps',
  //     '信号质量更新: SNR ' + (Math.random() * 5 + 20).toFixed(1) + 'dB',
  //     '数据缓存: ' + Math.floor(Math.random() * 30 + 70) + '% | 正常'
  //   ][Math.floor(Math.random() * 4)]
  // };
  // 使用新的数据格式进行测试
  const mockData = {
    "ChannelStatus": {
      "BroadcastType": "应急广播",
      "CurrentBitrate": "128 kbps",
      "DataAttribute": "音频数据"
    },
    "CurrentFileName": "emergency_broadcast_001.mp3",
    "TransmissionProgress": "75%",
    "ServiceOverview": [
      {
        "ServiceLabel1": "音频处理服务",
        "ServiceType": "音频编解码"
      },
      {
        "ServiceLabel2": "数据传输服务",
        "ServiceType": "网络传输"
      },
      {
        "ServiceLabel3": "监控管理服务",
        "ServiceType": "系统监控"
      },
      {
        "ServiceLabel4": "存储服务",
        "ServiceType": "数据存储"
      }
    ]
  };
  
  ws.send(JSON.stringify(mockData));
}

// 定期清理无效连接
setInterval(() => {
  const now = Date.now();
  for (const [id, connection] of activeConnections.entries()) {
    if (now - connection.lastRequest > 30000) { // 30秒无活动
      console.log('清理无效连接:', id);
      if (connection.tcp && !connection.tcp.destroyed) {
        connection.tcp.destroy();
      }
      if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }
      activeConnections.delete(id);
    }
  }
}, 10000); // 每10秒检查一次

// 处理进程退出
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  
  // 关闭所有连接
  for (const [id, connection] of activeConnections.entries()) {
    if (connection.tcp && !connection.tcp.destroyed) {
      connection.tcp.destroy();
    }
    if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.close();
    }
  }
  
  wss.close(() => {
    console.log('WebSocket服务器已关闭');
    process.exit(0);
  });
});
