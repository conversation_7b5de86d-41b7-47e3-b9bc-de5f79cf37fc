#!/bin/bash

echo "启动DRM监控系统..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: npm未安装，请先安装npm"
    exit 1
fi

# 安装前端依赖
echo "安装前端依赖..."
npm install

# 安装服务器依赖
echo "安装服务器依赖..."
cd server
npm install
cd ..

# 启动WebSocket代理服务器
echo "启动WebSocket代理服务器..."
cd server
npm start &
SERVER_PID=$!
cd ..

# 等待服务器启动
sleep 3

# 启动Vue开发服务器
echo "启动Vue开发服务器..."
npm run serve &
VUE_PID=$!

echo "系统启动完成!"
echo "前端地址: http://localhost:3000"
echo "WebSocket代理: ws://localhost:8083"
echo "TCP目标服务器: **************:9080"
echo ""
echo "按Ctrl+C停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $SERVER_PID $VUE_PID; exit" INT
wait
