<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <JSCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="false" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_GENERATOR_MULT" value="true" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="OBJECT_LITERAL_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="BLOCK_COMMENT_ADD_SPACE" value="true" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="IF_BRACE_FORCE" value="1" />
      <option name="DOWHILE_BRACE_FORCE" value="1" />
      <option name="WHILE_BRACE_FORCE" value="1" />
      <option name="FOR_BRACE_FORCE" value="1" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>